<<<<<<< HEAD
import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import aiInsightsService from '@/services/aiInsightsService';
=======
import { useState, useEffect, useCallback } from 'react';
import aiInsightsService from '@/services/aiInsightsService';
import { toast } from 'sonner';
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)

export const useRealTimeAI = (options = {}) => {
  const {
    autoStart = true,
    updateInterval = 30000,
    showNotifications = true,
<<<<<<< HEAD
    categories = null
=======
    categories = null // Filter by specific categories
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)
  } = options;

  const [insights, setInsights] = useState([]);
  const [isActive, setIsActive] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
<<<<<<< HEAD
  const unsubscribeRef = useRef(null);

  // Filter insights by categories if specified
  const filteredInsights = categories 
    ? insights.filter(insight => categories.includes(insight.category))
    : insights;

  // Statistics
  const totalInsights = filteredInsights.length;
  const newInsights = filteredInsights.filter(insight => 
    new Date() - new Date(insight.timestamp) < 300000 // 5 minutes
  ).length;
  const criticalInsights = filteredInsights.filter(insight => 
    insight.priority === 'critical'
  ).length;
  const highPriorityInsights = filteredInsights.filter(insight => 
    insight.priority === 'high'
  ).length;
  const actionableInsights = filteredInsights.filter(insight => 
    insight.recommendations && insight.recommendations.length > 0
  ).length;

  // Start AI insights
  const startAI = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Subscribe to insights updates
      const unsubscribe = aiInsightsService.subscribe((newInsights) => {
        setInsights(newInsights);
        
        // Show notifications for high-priority insights
        if (showNotifications) {
          const recentHighPriority = newInsights.filter(insight => {
            const isRecent = new Date() - new Date(insight.timestamp) < 10000; // 10 seconds
            return isRecent && (insight.priority === 'critical' || insight.priority === 'high');
          });
          
          recentHighPriority.forEach(insight => {
            toast.info(`🤖 AI Insight: ${insight.title}`, {
              description: insight.description,
              duration: 5000
            });
          });
        }
      });
      
      unsubscribeRef.current = unsubscribe;
      
      // Start the service
      aiInsightsService.startRealTimeInsights(updateInterval);
      setIsActive(true);
      
      return unsubscribe;
    } catch (err) {
      setError(err.message);
      console.error('Error starting AI insights:', err);
    } finally {
      setLoading(false);
    }
  }, [updateInterval, showNotifications]);

  // Stop AI insights
  const stopAI = useCallback(() => {
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
      unsubscribeRef.current = null;
    }
    aiInsightsService.stopRealTimeInsights();
    setIsActive(false);
  }, []);
=======

  // Handle insights updates
  const handleInsightsUpdate = useCallback((newInsights) => {
    let filteredInsights = newInsights;
    
    // Filter by categories if specified
    if (categories && Array.isArray(categories)) {
      filteredInsights = newInsights.filter(insight => 
        categories.includes(insight.category)
      );
    }

    // Check for new high-priority insights to show notifications
    if (showNotifications) {
      const newHighPriorityInsights = filteredInsights.filter(insight => 
        insight.isNew && (insight.priority === 'critical' || insight.priority === 'high')
      );

      newHighPriorityInsights.forEach(insight => {
        const toastOptions = {
          duration: 5000,
          action: {
            label: 'View',
            onClick: () => {
              // Mark as read when viewed
              aiInsightsService.markAsRead(insight.id);
            }
          }
        };

        if (insight.priority === 'critical') {
          toast.error(`🚨 ${insight.title}`, toastOptions);
        } else {
          toast.warning(`⚠️ ${insight.title}`, toastOptions);
        }
      });
    }

    setInsights(filteredInsights);
    setError(null);
  }, [categories, showNotifications]);

  // Start real-time AI insights
  const startAI = useCallback(async () => {
    if (isActive) return;
    
    setLoading(true);
    setError(null);
    
    try {
      // Subscribe to insights updates
      const unsubscribe = aiInsightsService.subscribe(handleInsightsUpdate);
      
      // Start the AI service
      aiInsightsService.startRealTimeInsights(updateInterval);
      
      setIsActive(true);
      
      if (showNotifications) {
        toast.success('🤖 AI Insights activated - Real-time analysis started');
      }
      
      // Store unsubscribe function for cleanup
      return unsubscribe;
    } catch (err) {
      setError(err.message);
      toast.error('Failed to start AI insights');
    } finally {
      setLoading(false);
    }
  }, [isActive, updateInterval, handleInsightsUpdate, showNotifications]);

  // Stop real-time AI insights
  const stopAI = useCallback(() => {
    if (!isActive) return;
    
    aiInsightsService.stopRealTimeInsights();
    setIsActive(false);
    
    if (showNotifications) {
      toast.info('🤖 AI Insights deactivated');
    }
  }, [isActive, showNotifications]);
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)

  // Toggle AI insights
  const toggleAI = useCallback(() => {
    if (isActive) {
      stopAI();
    } else {
      startAI();
    }
  }, [isActive, startAI, stopAI]);

<<<<<<< HEAD
  // Generate insights manually
  const generateInsights = useCallback(() => {
    if (isActive) {
      aiInsightsService.generateInsights();
      toast.success('Generating new AI insights...');
    }
  }, [isActive]);

  // Auto-start if enabled
  useEffect(() => {
    if (autoStart) {
      startAI().then(unsub => {
        unsubscribeRef.current = unsub;
      });
    }

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
=======
  // Generate new insights manually
  const generateInsights = useCallback(async () => {
    setLoading(true);
    try {
      await aiInsightsService.generateInsights();
      toast.success('🧠 New AI insights generated');
    } catch (err) {
      setError(err.message);
      toast.error('Failed to generate insights');
    } finally {
      setLoading(false);
    }
  }, []);

  // Mark insight as read
  const markAsRead = useCallback((insightId) => {
    aiInsightsService.markAsRead(insightId);
  }, []);

  // Clear all insights
  const clearInsights = useCallback(() => {
    aiInsightsService.clearInsights();
    toast.info('All insights cleared');
  }, []);

  // Get insights by priority
  const getInsightsByPriority = useCallback((priority) => {
    return insights.filter(insight => insight.priority === priority);
  }, [insights]);

  // Get actionable insights
  const getActionableInsights = useCallback(() => {
    return insights.filter(insight => insight.actionable);
  }, [insights]);

  // Get new insights count
  const getNewInsightsCount = useCallback(() => {
    return insights.filter(insight => insight.isNew).length;
  }, [insights]);

  // Auto-start if enabled
  useEffect(() => {
    let unsubscribe;
    
    if (autoStart) {
      startAI().then(unsub => {
        unsubscribe = unsub;
      });
    }

    // Cleanup on unmount
    return () => {
      if (unsubscribe) {
        unsubscribe();
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)
      }
      if (isActive) {
        aiInsightsService.stopRealTimeInsights();
      }
    };
  }, [autoStart, startAI]);

<<<<<<< HEAD
  return {
    insights: filteredInsights,
    isActive,
    loading,
    error,
=======
  // Get current insights on mount
  useEffect(() => {
    const currentInsights = aiInsightsService.getInsights();
    handleInsightsUpdate(currentInsights);
  }, [handleInsightsUpdate]);

  return {
    // State
    insights,
    isActive,
    loading,
    error,
    
    // Actions
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)
    startAI,
    stopAI,
    toggleAI,
    generateInsights,
<<<<<<< HEAD
    // Statistics
    totalInsights,
    newInsights,
    criticalInsights,
    highPriorityInsights,
    actionableInsights
  };
};

export default useRealTimeAI;
=======
    markAsRead,
    clearInsights,
    
    // Utilities
    getInsightsByPriority,
    getActionableInsights,
    getNewInsightsCount,
    
    // Stats
    totalInsights: insights.length,
    newInsights: getNewInsightsCount(),
    criticalInsights: getInsightsByPriority('critical').length,
    highPriorityInsights: getInsightsByPriority('high').length
  };
};

// Hook for specific insight categories
export const useRealTimeAIByCategory = (category, options = {}) => {
  return useRealTimeAI({
    ...options,
    categories: [category]
  });
};

// Hook for high-priority insights only
export const useHighPriorityAI = (options = {}) => {
  const aiData = useRealTimeAI(options);
  
  const highPriorityInsights = aiData.insights.filter(insight => 
    insight.priority === 'critical' || insight.priority === 'high'
  );

  return {
    ...aiData,
    insights: highPriorityInsights
  };
};

// Hook for actionable insights only
export const useActionableAI = (options = {}) => {
  const aiData = useRealTimeAI(options);
  
  const actionableInsights = aiData.insights.filter(insight => insight.actionable);

  return {
    ...aiData,
    insights: actionableInsights
  };
};
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)
