<<<<<<< HEAD
import React, { useState, useEffect, useRef } from 'react';
import { 
  LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell,
  ComposedChart, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
=======
import React, { useState, useEffect } from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
  RadialBarChart,
  RadialBar,
  Scatter<PERSON>hart,
  Scatter
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
<<<<<<< HEAD
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  TrendingUp, TrendingDown, BarChart3, PieChart as PieChartIcon, 
  LineChart as LineChartIcon, Activity, Download, RefreshCw, 
  Calendar, Filter, Maximize2, Minimize2
} from 'lucide-react';
import { useGraphData } from '@/hooks/useGraphData';
import { toast } from 'sonner';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

const ComprehensiveGraphs = ({ 
  title = "Business Analytics", 
  showControls = true,
  defaultType = "line",
  height = 400,
  data = null,
  autoRefresh = true,
  refreshInterval = 30000,
  exportable = true,
  fullscreen = false,
  onFullscreenToggle = null
}) => {
  const [chartType, setChartType] = useState(defaultType);
  const [timeRange, setTimeRange] = useState('30d');
  const [isFullscreen, setIsFullscreen] = useState(fullscreen);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const containerRef = useRef(null);

  const { 
    graphData, 
    loading, 
    error, 
    refreshData,
    multipleGraphData,
    generateInsights
  } = useGraphData({
    type: chartType,
    timeRange,
    autoRefresh,
    refreshInterval
  });

  const chartData = data || graphData;

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(() => {
        handleRefresh();
      }, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshData();
      toast.success('Data refreshed successfully');
    } catch (error) {
      toast.error('Failed to refresh data');
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleExport = () => {
    try {
      const dataStr = JSON.stringify(chartData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${title.replace(/\s+/g, '_')}_data_${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      URL.revokeObjectURL(url);
      toast.success('Data exported successfully');
    } catch (error) {
      toast.error('Failed to export data');
    }
  };

  const toggleFullscreen = () => {
    const newFullscreen = !isFullscreen;
    setIsFullscreen(newFullscreen);
    if (onFullscreenToggle) {
      onFullscreenToggle(newFullscreen);
    }
  };

  const renderChart = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading chart data...</span>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-64 text-red-500">
          <Activity className="h-8 w-8 mr-2" />
          <span>Error loading chart: {error}</span>
        </div>
      );
    }

    if (!chartData || chartData.length === 0) {
      return (
        <div className="flex items-center justify-center h-64 text-gray-500">
          <BarChart3 className="h-8 w-8 mr-2" />
          <span>No data available</span>
=======
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  ShoppingCart,
  Package,
  Calendar,
  BarChart3,
  PieChart as PieChartIcon,
  Activity,
  RefreshCw,
  Download,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { toast } from 'sonner';

const ComprehensiveGraphs = ({ 
  data = null, 
  type = 'line', 
  title = 'Analytics Chart',
  description = '',
  height = 300,
  showControls = true,
  autoRefresh = false,
  refreshInterval = 30000,
  className = '',
  onDataUpdate = null
}) => {
  const [chartType, setChartType] = useState(type);
  const [timeRange, setTimeRange] = useState('7d');
  const [chartData, setChartData] = useState(data);
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());

  // Color schemes for different chart types
  const colorSchemes = {
    primary: ['#10b981', '#059669', '#047857', '#065f46'],
    profit: ['#22c55e', '#16a34a', '#15803d', '#166534'],
    loss: ['#ef4444', '#dc2626', '#b91c1c', '#991b1b'],
    neutral: ['#6b7280', '#4b5563', '#374151', '#1f2937'],
    rainbow: ['#10b981', '#3b82f6', '#8b5cf6', '#f59e0b', '#ef4444', '#06b6d4']
  };

  // Generate mock data if none provided
  const generateMockData = (type, range) => {
    const days = range === '7d' ? 7 : range === '30d' ? 30 : range === '90d' ? 90 : 365;
    const data = [];
    
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - (days - i - 1));
      
      const baseValue = 1000 + Math.random() * 500;
      const trend = type === 'profit' ? i * 10 : type === 'loss' ? -i * 5 : Math.sin(i / 5) * 100;
      
      data.push({
        date: date.toISOString().split('T')[0],
        name: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        value: Math.max(0, baseValue + trend + (Math.random() - 0.5) * 200),
        profit: Math.max(0, baseValue * 0.3 + trend * 0.5 + (Math.random() - 0.5) * 100),
        loss: Math.max(0, baseValue * 0.1 - trend * 0.3 + (Math.random() - 0.5) * 50),
        revenue: baseValue + trend + (Math.random() - 0.5) * 300,
        orders: Math.floor(20 + Math.random() * 50 + i * 2),
        customers: Math.floor(10 + Math.random() * 30 + i),
        products: Math.floor(5 + Math.random() * 15),
        growth: ((Math.random() - 0.5) * 20).toFixed(1)
      });
    }
    
    return data;
  };

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        refreshData();
      }, refreshInterval);
      
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, timeRange]);

  // Initialize data
  useEffect(() => {
    if (!data) {
      setChartData(generateMockData(type, timeRange));
    } else {
      setChartData(data);
    }
  }, [data, type, timeRange]);

  const refreshData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newData = data || generateMockData(type, timeRange);
      setChartData(newData);
      setLastUpdated(new Date());
      
      if (onDataUpdate) {
        onDataUpdate(newData);
      }
      
      toast.success('Chart data updated successfully');
    } catch (error) {
      toast.error('Failed to update chart data');
    } finally {
      setLoading(false);
    }
  };

  const exportChart = () => {
    toast.info('Exporting chart data...');
    // Implement export functionality
    const csvData = chartData.map(item => 
      Object.entries(item).map(([key, value]) => `${key}: ${value}`).join(', ')
    ).join('\n');
    
    const blob = new Blob([csvData], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getColorScheme = () => {
    if (title.toLowerCase().includes('profit') || title.toLowerCase().includes('revenue')) {
      return colorSchemes.profit;
    }
    if (title.toLowerCase().includes('loss') || title.toLowerCase().includes('expense')) {
      return colorSchemes.loss;
    }
    return colorSchemes.primary;
  };

  const colors = getColorScheme();

  const renderChart = () => {
    if (!chartData || chartData.length === 0) {
      return (
        <div className="flex items-center justify-center h-64 text-muted-foreground">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No data available</p>
          </div>
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)
        </div>
      );
    }

    const commonProps = {
      data: chartData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 }
    };

    switch (chartType) {
      case 'line':
        return (
          <LineChart {...commonProps}>
<<<<<<< HEAD
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey="value" stroke="#8884d8" strokeWidth={2} />
            <Line type="monotone" dataKey="value2" stroke="#82ca9d" strokeWidth={2} />
=======
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis dataKey="name" stroke="#666" />
            <YAxis stroke="#666" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: '#fff', 
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }} 
            />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="value" 
              stroke={colors[0]} 
              strokeWidth={3}
              dot={{ fill: colors[0], strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: colors[0], strokeWidth: 2 }}
            />
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)
          </LineChart>
        );

      case 'area':
        return (
          <AreaChart {...commonProps}>
<<<<<<< HEAD
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Area type="monotone" dataKey="value" stackId="1" stroke="#8884d8" fill="#8884d8" />
            <Area type="monotone" dataKey="value2" stackId="1" stroke="#82ca9d" fill="#82ca9d" />
=======
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis dataKey="name" stroke="#666" />
            <YAxis stroke="#666" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: '#fff', 
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }} 
            />
            <Legend />
            <Area 
              type="monotone" 
              dataKey="value" 
              stroke={colors[0]} 
              fill={colors[0]}
              fillOpacity={0.3}
              strokeWidth={2}
            />
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)
          </AreaChart>
        );

      case 'bar':
        return (
          <BarChart {...commonProps}>
<<<<<<< HEAD
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="value" fill="#8884d8" />
            <Bar dataKey="value2" fill="#82ca9d" />
=======
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis dataKey="name" stroke="#666" />
            <YAxis stroke="#666" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: '#fff', 
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }} 
            />
            <Legend />
            <Bar dataKey="value" fill={colors[0]} radius={[4, 4, 0, 0]} />
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)
          </BarChart>
        );

      case 'pie':
<<<<<<< HEAD
        return (
          <PieChart {...commonProps}>
            <Pie
              data={chartData}
=======
        const pieData = chartData.slice(0, 6).map((item, index) => ({
          name: item.name,
          value: item.value,
          fill: colorSchemes.rainbow[index % colorSchemes.rainbow.length]
        }));
        
        return (
          <PieChart>
            <Pie
              data={pieData}
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
<<<<<<< HEAD
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
=======
              {pieData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)
              ))}
            </Pie>
            <Tooltip />
          </PieChart>
        );

      case 'composed':
        return (
          <ComposedChart {...commonProps}>
<<<<<<< HEAD
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Area type="monotone" dataKey="value2" fill="#8884d8" stroke="#8884d8" />
            <Bar dataKey="value" barSize={20} fill="#413ea0" />
            <Line type="monotone" dataKey="profit" stroke="#ff7300" />
=======
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis dataKey="name" stroke="#666" />
            <YAxis stroke="#666" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: '#fff', 
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }} 
            />
            <Legend />
            <Bar dataKey="profit" fill={colorSchemes.profit[0]} radius={[2, 2, 0, 0]} />
            <Line type="monotone" dataKey="revenue" stroke={colors[0]} strokeWidth={3} />
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)
          </ComposedChart>
        );

      default:
        return renderChart();
    }
  };

<<<<<<< HEAD
  return (
    <Card className={`w-full ${isFullscreen ? 'fixed inset-0 z-50 m-4' : ''}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-base font-medium">{title}</CardTitle>
          <CardDescription>
            Interactive business analytics and insights
          </CardDescription>
        </div>
        
        {showControls && (
          <div className="flex items-center space-x-2">
            <Select value={chartType} onValueChange={setChartType}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="line">
                  <div className="flex items-center">
                    <LineChartIcon className="h-4 w-4 mr-2" />
                    Line
                  </div>
                </SelectItem>
                <SelectItem value="area">
                  <div className="flex items-center">
                    <Activity className="h-4 w-4 mr-2" />
                    Area
                  </div>
                </SelectItem>
                <SelectItem value="bar">
                  <div className="flex items-center">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Bar
                  </div>
                </SelectItem>
                <SelectItem value="pie">
                  <div className="flex items-center">
                    <PieChartIcon className="h-4 w-4 mr-2" />
                    Pie
                  </div>
                </SelectItem>
                <SelectItem value="composed">
                  <div className="flex items-center">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Mixed
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">7D</SelectItem>
                <SelectItem value="30d">30D</SelectItem>
                <SelectItem value="90d">90D</SelectItem>
                <SelectItem value="1y">1Y</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>

            {exportable && (
              <Button variant="outline" size="sm" onClick={handleExport}>
                <Download className="h-4 w-4" />
              </Button>
            )}

            <Button variant="outline" size="sm" onClick={toggleFullscreen}>
              {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </Button>
=======
  const chartTypes = [
    { value: 'line', label: 'Line Chart', icon: Activity },
    { value: 'area', label: 'Area Chart', icon: TrendingUp },
    { value: 'bar', label: 'Bar Chart', icon: BarChart3 },
    { value: 'pie', label: 'Pie Chart', icon: PieChartIcon },
    { value: 'composed', label: 'Combined', icon: BarChart3 }
  ];

  const timeRanges = [
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' },
    { value: '1y', label: '1 Year' }
  ];

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-green-600" />
              {title}
            </CardTitle>
            {description && (
              <CardDescription>{description}</CardDescription>
            )}
          </div>
          
          {showControls && (
            <div className="flex items-center space-x-2">
              <Select value={chartType} onValueChange={setChartType}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {chartTypes.map((type) => {
                    const Icon = type.icon;
                    return (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center gap-2">
                          <Icon className="h-4 w-4" />
                          {type.label}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timeRanges.map((range) => (
                    <SelectItem key={range.value} value={range.value}>
                      {range.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Button
                variant="outline"
                size="sm"
                onClick={refreshData}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={exportChart}
              >
                <Download className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
        
        {lastUpdated && (
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            Last updated: {lastUpdated.toLocaleTimeString()}
            {autoRefresh && (
              <Badge variant="secondary" className="text-xs">
                Auto-refresh
              </Badge>
            )}
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)
          </div>
        )}
      </CardHeader>
      
      <CardContent>
<<<<<<< HEAD
        <div style={{ height: isFullscreen ? 'calc(100vh - 200px)' : height }}>
          <ResponsiveContainer width="100%" height="100%">
            {renderChart()}
          </ResponsiveContainer>
        </div>
        
        {chartData && chartData.length > 0 && (
          <div className="mt-4 flex flex-wrap gap-2">
            <Badge variant="secondary">
              <Calendar className="h-3 w-3 mr-1" />
              {chartData.length} data points
            </Badge>
            <Badge variant="secondary">
              <TrendingUp className="h-3 w-3 mr-1" />
              {chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart
            </Badge>
            {autoRefresh && (
              <Badge variant="outline">
                <RefreshCw className="h-3 w-3 mr-1" />
                Auto-refresh: {refreshInterval / 1000}s
              </Badge>
            )}
          </div>
        )}
=======
        <ResponsiveContainer width="100%" height={height}>
          {renderChart()}
        </ResponsiveContainer>
>>>>>>> fc96b82 (Add comprehensive graphs, real-time AI insights, interactive maps, and automatic updates for existing users)
      </CardContent>
    </Card>
  );
};

export default ComprehensiveGraphs;
