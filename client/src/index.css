@import "tailwindcss";
@import "tw-animate-css";

/* CSS Variables for Dynamic Theming */
:root {
  --font-size-base: 16px;
  --border-radius-base: 0.5rem;
  --spacing-scale: 1;
  --color-primary: #3b82f6;
  --color-secondary: #64748b;
  --color-accent: #10b981;
  --color-background: #ffffff;
  --color-foreground: #1f2937;
  --color-muted: #f8fafc;
  --color-muted-foreground: #64748b;
  --color-border: #e2e8f0;
  --color-input: #ffffff;
  --color-ring: #3b82f6;
  --color-destructive: #ef4444;
  --color-destructive-foreground: #ffffff;
  --color-success: #10b981;
  --color-success-foreground: #ffffff;
  --color-warning: #f59e0b;
  --color-warning-foreground: #ffffff;
  --transition-duration: 0.2s;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Theme Classes */
[data-theme="dark"] {
  --color-background: #0f172a;
  --color-foreground: #f1f5f9;
  --color-muted: #1e293b;
  --color-muted-foreground: #94a3b8;
  --color-border: #334155;
  --color-input: #1e293b;
  --color-primary: #3b82f6;
  --color-secondary: #64748b;
  --color-accent: #10b981;
  --color-destructive: #ef4444;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
}

[data-theme="blue"] {
  --color-primary: #2563eb;
  --color-accent: #1d4ed8;
  --color-ring: #2563eb;
}

[data-theme="green"] {
  --color-primary: #059669;
  --color-accent: #047857;
  --color-ring: #059669;
}

[data-theme="purple"] {
  --color-primary: #7c3aed;
  --color-accent: #6d28d9;
  --color-ring: #7c3aed;
}

[data-theme="rose"] {
  --color-primary: #e11d48;
  --color-accent: #be185d;
  --color-ring: #e11d48;
}

.high-contrast {
  --color-background: #000000;
  --color-foreground: #ffffff;
  --color-primary: #ffffff;
  --color-secondary: #cccccc;
  --color-muted: #1a1a1a;
  --color-muted-foreground: #cccccc;
  --color-border: #666666;
  --color-input: #1a1a1a;
}

.reduce-motion,
.reduce-motion *,
.reduce-motion *::before,
.reduce-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

.no-animations,
.no-animations *,
.no-animations *::before,
.no-animations *::after {
  animation: none !important;
  transition: none !important;
}

/* Compact Mode */
.compact-mode {
  --spacing-scale: 0.75;
}

.compact-mode .p-4 {
  padding: calc(1rem * var(--spacing-scale));
}

/* Smooth Transitions */
* {
  transition:
    background-color var(--transition-duration) ease,
    border-color var(--transition-duration) ease,
    color var(--transition-duration) ease,
    box-shadow var(--transition-duration) ease;
}

/* Enhanced Button Styles */
.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-background);
  border: 1px solid var(--color-primary);
  border-radius: var(--border-radius-base);
  padding: 0.5rem 1rem;
  font-size: var(--font-size-base);
  transition: all var(--transition-duration) ease;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background-color: var(--color-accent);
  border-color: var(--color-accent);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* Enhanced Card Styles */
.card-enhanced {
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-base);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-duration) ease;
}

.card-enhanced:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* Theme Toggle Animation */
.theme-toggle {
  position: relative;
  overflow: hidden;
}

.theme-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.theme-toggle:hover::before {
  left: 100%;
}

.compact-mode .p-6 {
  padding: calc(1.5rem * var(--spacing-scale));
}

.compact-mode .gap-4 {
  gap: calc(1rem * var(--spacing-scale));
}

.compact-mode .gap-6 {
  gap: calc(1.5rem * var(--spacing-scale));
}

/* Theme Transitions */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.no-animations * {
  transition: none !important;
}

/* Enhanced Theme Classes */
.theme-light {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
}

.theme-dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
}

/* Custom Scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: var(--border-radius-base);
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: var(--border-radius-base);
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Focus Styles */
.focus-visible:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(90deg,
    hsl(var(--muted)) 0%,
    hsl(var(--muted-foreground) / 0.1) 50%,
    hsl(var(--muted)) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* PWA Animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-slide-up {
  animation: slide-up 0.4s ease-out;
}

@theme {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

/* Blue Theme */
.blue {
  --background: oklch(0.98 0.02 240);
  --foreground: oklch(0.15 0.05 240);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.05 240);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.05 240);
  --primary: oklch(0.55 0.15 240);
  --primary-foreground: oklch(0.98 0.02 240);
  --secondary: oklch(0.95 0.02 240);
  --secondary-foreground: oklch(0.15 0.05 240);
  --muted: oklch(0.95 0.02 240);
  --muted-foreground: oklch(0.45 0.05 240);
  --accent: oklch(0.95 0.02 240);
  --accent-foreground: oklch(0.15 0.05 240);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.9 0.02 240);
  --input: oklch(0.9 0.02 240);
  --ring: oklch(0.55 0.15 240);
}

/* Green Theme */
.green {
  --background: oklch(0.98 0.02 120);
  --foreground: oklch(0.15 0.05 120);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.05 120);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.05 120);
  --primary: oklch(0.55 0.15 120);
  --primary-foreground: oklch(0.98 0.02 120);
  --secondary: oklch(0.95 0.02 120);
  --secondary-foreground: oklch(0.15 0.05 120);
  --muted: oklch(0.95 0.02 120);
  --muted-foreground: oklch(0.45 0.05 120);
  --accent: oklch(0.95 0.02 120);
  --accent-foreground: oklch(0.15 0.05 120);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.9 0.02 120);
  --input: oklch(0.9 0.02 120);
  --ring: oklch(0.55 0.15 120);
}

/* Purple Theme */
.purple {
  --background: oklch(0.98 0.02 280);
  --foreground: oklch(0.15 0.05 280);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.05 280);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.05 280);
  --primary: oklch(0.55 0.15 280);
  --primary-foreground: oklch(0.98 0.02 280);
  --secondary: oklch(0.95 0.02 280);
  --secondary-foreground: oklch(0.15 0.05 280);
  --muted: oklch(0.95 0.02 280);
  --muted-foreground: oklch(0.45 0.05 280);
  --accent: oklch(0.95 0.02 280);
  --accent-foreground: oklch(0.15 0.05 280);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.9 0.02 280);
  --input: oklch(0.9 0.02 280);
  --ring: oklch(0.55 0.15 280);
}

/* Orange Theme */
.orange {
  --background: oklch(0.98 0.02 40);
  --foreground: oklch(0.15 0.05 40);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.05 40);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.05 40);
  --primary: oklch(0.65 0.18 40);
  --primary-foreground: oklch(0.98 0.02 40);
  --secondary: oklch(0.95 0.02 40);
  --secondary-foreground: oklch(0.15 0.05 40);
  --muted: oklch(0.95 0.02 40);
  --muted-foreground: oklch(0.45 0.05 40);
  --accent: oklch(0.95 0.02 40);
  --accent-foreground: oklch(0.15 0.05 40);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.9 0.02 40);
  --input: oklch(0.9 0.02 40);
  --ring: oklch(0.65 0.18 40);
}

/* Rose Theme */
.rose {
  --background: oklch(0.98 0.02 350);
  --foreground: oklch(0.15 0.05 350);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.05 350);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.05 350);
  --primary: oklch(0.6 0.15 350);
  --primary-foreground: oklch(0.98 0.02 350);
  --secondary: oklch(0.95 0.02 350);
  --secondary-foreground: oklch(0.15 0.05 350);
  --muted: oklch(0.95 0.02 350);
  --muted-foreground: oklch(0.45 0.05 350);
  --accent: oklch(0.95 0.02 350);
  --accent-foreground: oklch(0.15 0.05 350);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.9 0.02 350);
  --input: oklch(0.9 0.02 350);
  --ring: oklch(0.6 0.15 350);
}

/* Slate Theme */
.slate {
  --background: oklch(0.98 0.005 220);
  --foreground: oklch(0.15 0.01 220);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.01 220);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.01 220);
  --primary: oklch(0.4 0.05 220);
  --primary-foreground: oklch(0.98 0.005 220);
  --secondary: oklch(0.95 0.005 220);
  --secondary-foreground: oklch(0.15 0.01 220);
  --muted: oklch(0.95 0.005 220);
  --muted-foreground: oklch(0.45 0.01 220);
  --accent: oklch(0.95 0.005 220);
  --accent-foreground: oklch(0.15 0.01 220);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.9 0.005 220);
  --input: oklch(0.9 0.005 220);
  --ring: oklch(0.4 0.05 220);
}

/* Emerald Theme */
.emerald {
  --background: oklch(0.98 0.02 150);
  --foreground: oklch(0.15 0.05 150);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.05 150);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.05 150);
  --primary: oklch(0.55 0.15 150);
  --primary-foreground: oklch(0.98 0.02 150);
  --secondary: oklch(0.95 0.02 150);
  --secondary-foreground: oklch(0.15 0.05 150);
  --muted: oklch(0.95 0.02 150);
  --muted-foreground: oklch(0.45 0.05 150);
  --accent: oklch(0.95 0.02 150);
  --accent-foreground: oklch(0.15 0.05 150);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.9 0.02 150);
  --input: oklch(0.9 0.02 150);
  --ring: oklch(0.55 0.15 150);
}

/* Fresh White Theme */
.fresh {
  --background: oklch(1 0 0);
  --foreground: oklch(0.1 0.02 150);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.1 0.02 150);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.1 0.02 150);
  --primary: oklch(0.55 0.15 150);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.95 0.02 150);
  --secondary-foreground: oklch(0.1 0.02 150);
  --muted: oklch(0.97 0.01 150);
  --muted-foreground: oklch(0.45 0.02 150);
  --accent: oklch(0.93 0.03 150);
  --accent-foreground: oklch(0.1 0.02 150);
  --destructive: oklch(0.62 0.2 25);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.9 0.02 150);
  --input: oklch(0.95 0.01 150);
  --ring: oklch(0.55 0.15 150);
}

/* Indigo Theme */
.indigo {
  --background: oklch(0.98 0.02 260);
  --foreground: oklch(0.15 0.05 260);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.05 260);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.05 260);
  --primary: oklch(0.5 0.15 260);
  --primary-foreground: oklch(0.98 0.02 260);
  --secondary: oklch(0.95 0.02 260);
  --secondary-foreground: oklch(0.15 0.05 260);
  --muted: oklch(0.95 0.02 260);
  --muted-foreground: oklch(0.45 0.05 260);
  --accent: oklch(0.95 0.02 260);
  --accent-foreground: oklch(0.15 0.05 260);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.9 0.02 260);
  --input: oklch(0.9 0.02 260);
  --ring: oklch(0.5 0.15 260);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-size: var(--font-size-base, 16px);
    transition: font-size var(--transition-duration, 0.2s) ease;
  }

  /* Custom animations */
  @keyframes bounce-in {
    0% { transform: scale(0); opacity: 0; }
    50% { transform: scale(1.1); opacity: 1; }
    100% { transform: scale(1); opacity: 1; }
  }

  @keyframes fade-in {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .animate-bounce-in {
    animation: bounce-in var(--animation-duration, 0.3s) ease-out;
  }

  .animate-fade-in {
    animation: fade-in var(--animation-duration, 0.3s) ease-out;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}