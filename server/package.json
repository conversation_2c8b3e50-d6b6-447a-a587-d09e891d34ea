{"name": "server", "version": "1.0.0", "description": "", "main": "sever.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.12.1", "dependencies": {"axios": "^1.12.2", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cloudinary": "^1.41.3", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "geoip-lite": "^1.4.10", "jsonwebtoken": "^9.0.2", "mongoose": "^8.18.2", "multer": "^2.0.2", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^7.0.6", "nodemon": "^3.1.10", "request": "^2.88.2", "socket.io": "^4.8.1", "twilio": "^5.9.0", "ua-parser-js": "^2.0.5"}}