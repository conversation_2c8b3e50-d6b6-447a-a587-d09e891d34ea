const axios = require('axios');

async function testRegistration() {
  try {
    console.log('Testing user registration...');
    
    const response = await axios.post('http://localhost:5000/api/auth/register', {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'testpass123',
      phone: '+1234567890',
      businessName: 'Test Business'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Registration successful!');
    console.log('Response:', response.data);
    
  } catch (error) {
    console.log('❌ Registration failed!');
    console.log('Error:', error.response?.data || error.message);
    console.log('Status:', error.response?.status);
  }
}

testRegistration();
